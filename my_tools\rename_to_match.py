#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片文件名匹配重命名工具

功能：
将222文件夹下的图片文件名改成与111文件夹下的图片文件名一致
按照文件排序顺序一一对应重命名

示例：
222文件夹: 00000000_pred.png -> 111文件夹: frame_000000.png
222文件夹: 00000001_pred.png -> 111文件夹: frame_000001.png
...

作者: [你的名字]
日期: [日期]
"""

import os
import shutil
from pathlib import Path


def get_image_files(folder_path, extensions=None):
    """
    获取文件夹中的图片文件列表
    
    Args:
        folder_path (str): 文件夹路径
        extensions (list): 支持的文件扩展名，默认为常见图片格式
    
    Returns:
        list: 排序后的图片文件路径列表
    """
    if extensions is None:
        extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.tif']
    
    folder = Path(folder_path)
    if not folder.exists():
        print(f"❌ 文件夹不存在: {folder_path}")
        return []
    
    image_files = []
    for ext in extensions:
        image_files.extend(folder.glob(f"*{ext}"))
        image_files.extend(folder.glob(f"*{ext.upper()}"))
    
    # 按文件名排序
    return sorted(image_files)


def rename_files_to_match(source_folder, reference_folder, backup=True):
    """
    将源文件夹中的文件重命名为与参考文件夹中文件名一致
    
    Args:
        source_folder (str): 需要重命名的文件夹路径 (444文件夹)
        reference_folder (str): 参考文件夹路径 (333文件夹)
        backup (bool): 是否创建备份
    
    Returns:
        bool: 操作是否成功
    """
    print("🔄 开始文件名匹配重命名...")
    print(f"📁 源文件夹 (需要重命名): {source_folder}")
    print(f"📁 参考文件夹 (目标命名): {reference_folder}")
    print("=" * 60)
    
    # 获取两个文件夹的图片文件
    source_files = get_image_files(source_folder)
    reference_files = get_image_files(reference_folder)
    
    if not source_files:
        print(f"❌ 源文件夹中没有找到图片文件: {source_folder}")
        return False
    
    if not reference_files:
        print(f"❌ 参考文件夹中没有找到图片文件: {reference_folder}")
        return False
    
    print(f"📊 源文件夹图片数量: {len(source_files)}")
    print(f"📊 参考文件夹图片数量: {len(reference_files)}")
    
    # 检查文件数量是否匹配
    if len(source_files) != len(reference_files):
        print("⚠️  警告：两个文件夹中的图片数量不一致！")
        print("将按照较少的数量进行匹配...")
        min_count = min(len(source_files), len(reference_files))
        source_files = source_files[:min_count]
        reference_files = reference_files[:min_count]
    
    # 创建备份文件夹
    if backup:
        backup_folder = Path(source_folder) / "backup_original_names"
        backup_folder.mkdir(exist_ok=True)
        print(f"💾 创建备份文件夹: {backup_folder}")
    
    print("\n开始重命名...")
    print("-" * 60)
    
    success_count = 0
    
    # 执行重命名
    for i, (source_file, reference_file) in enumerate(zip(source_files, reference_files)):
        try:
            # 获取参考文件的文件名（不包含路径）
            new_name = reference_file.name
            
            # 构建新的文件路径
            new_path = source_file.parent / new_name
            
            # 如果新文件名已存在且不是当前文件，先处理冲突
            if new_path.exists() and new_path != source_file:
                temp_name = f"temp_{i}_{new_name}"
                temp_path = source_file.parent / temp_name
                shutil.move(str(new_path), str(temp_path))
                print(f"⚠️  临时重命名冲突文件: {new_name} -> {temp_name}")
            
            # 创建备份
            if backup:
                backup_path = backup_folder / source_file.name
                shutil.copy2(str(source_file), str(backup_path))
            
            # 执行重命名
            shutil.move(str(source_file), str(new_path))
            
            print(f"[{i+1:3d}/{len(source_files)}] ✅ {source_file.name} -> {new_name}")
            success_count += 1
            
        except Exception as e:
            print(f"[{i+1:3d}/{len(source_files)}] ❌ {source_file.name} - 错误: {str(e)}")
    
    print("-" * 60)
    print(f"🎉 重命名完成！成功: {success_count}/{len(source_files)}")
    
    if backup and success_count > 0:
        print(f"💾 原始文件已备份到: {backup_folder}")
    
    return success_count > 0


def preview_rename_plan(source_folder, reference_folder):
    """
    预览重命名计划，不实际执行
    
    Args:
        source_folder (str): 源文件夹路径
        reference_folder (str): 参考文件夹路径
    """
    print("👀 预览重命名计划...")
    print("=" * 60)
    
    source_files = get_image_files(source_folder)
    reference_files = get_image_files(reference_folder)
    
    if not source_files or not reference_files:
        print("❌ 无法获取文件列表")
        return
    
    min_count = min(len(source_files), len(reference_files))
    
    print(f"将重命名前 {min_count} 个文件：")
    print("-" * 60)
    
    for i in range(min(10, min_count)):  # 只显示前10个
        source_name = source_files[i].name
        target_name = reference_files[i].name
        print(f"{i+1:2d}. {source_name} -> {target_name}")
    
    if min_count > 10:
        print(f"... 还有 {min_count - 10} 个文件")
    
    print("-" * 60)


def main():
    """主函数"""
    print("🔄 图片文件名匹配重命名工具")
    print("=" * 40)
    
    # ================================
    # 配置区域 - 修改这里的路径
    # ================================
    
    # 需要重命名的文件夹 (444文件夹)
    source_folder = "C:/qyh/mmsegmentation-main/my_tools/out/444"

    # 参考文件夹 (333文件夹)
    reference_folder = "C:/qyh/mmsegmentation-main/my_tools/out/333"
    
    # 是否创建备份
    create_backup = True
    
    # ================================
    # 执行操作
    # ================================
    
    print(f"📁 源文件夹: {source_folder}")
    print(f"📁 参考文件夹: {reference_folder}")
    print(f"💾 创建备份: {'是' if create_backup else '否'}")
    print()
    
    # 检查文件夹是否存在
    if not os.path.exists(source_folder):
        print(f"❌ 源文件夹不存在: {source_folder}")
        return
    
    if not os.path.exists(reference_folder):
        print(f"❌ 参考文件夹不存在: {reference_folder}")
        return
    
    # 预览重命名计划
    preview_rename_plan(source_folder, reference_folder)
    
    # 确认执行
    confirm = input("\n是否执行重命名？(y/n): ").strip().lower()
    
    if confirm in ['y', 'yes', '是']:
        # 执行重命名
        rename_files_to_match(source_folder, reference_folder, create_backup)
    else:
        print("❌ 操作已取消")


if __name__ == "__main__":
    main()


# ================================
# 使用示例和其他功能
# ================================

def batch_rename_multiple_folders():
    """批量处理多个文件夹的示例"""
    
    # 示例：如果有多个类似的文件夹需要处理
    folder_pairs = [
        ("C:/qyh/mmsegmentation-main/my_tools/out/444",
         "C:/qyh/mmsegmentation-main/my_tools/out/333"),
        # 可以添加更多文件夹对
        # ("path/to/another/source", "path/to/another/reference"),
    ]
    
    for source, reference in folder_pairs:
        print(f"\n处理文件夹对: {source} -> {reference}")
        rename_files_to_match(source, reference, backup=True)


# 如果需要批量处理多个文件夹，取消下面的注释
# batch_rename_multiple_folders()
