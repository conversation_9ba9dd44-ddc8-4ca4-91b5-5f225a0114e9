#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PNG转JPG格式转换工具

功能：
1. 单个文件转换
2. 批量文件夹转换
3. 支持透明背景处理（PNG特有）
4. 可自定义输出质量
5. 保持原始文件名结构

作者: [你的名字]
日期: [日期]
"""

import os
import sys
from PIL import Image
import argparse
from pathlib import Path


class PngToJpgConverter:
    """PNG转JPG转换器类"""
    
    def __init__(self, quality=95, background_color=(255, 255, 255)):
        """
        初始化转换器
        
        Args:
            quality (int): JPG图片质量 (1-100)，默认95
            background_color (tuple): 透明背景的替换颜色，默认白色
        """
        self.quality = quality
        self.background_color = background_color
    
    def convert_single_image(self, input_path, output_path=None):
        """
        转换单个PNG图片为JPG
        
        Args:
            input_path (str): 输入PNG文件路径
            output_path (str): 输出JPG文件路径，如果为None则自动生成
            
        Returns:
            bool: 转换是否成功
        """
        try:
            # 检查输入文件是否存在
            if not os.path.exists(input_path):
                print(f"错误：输入文件不存在 - {input_path}")
                return False
            
            # 检查文件扩展名
            if not input_path.lower().endswith('.png'):
                print(f"警告：输入文件不是PNG格式 - {input_path}")
            
            # 生成输出路径
            if output_path is None:
                output_path = input_path.rsplit('.', 1)[0] + '.jpg'
            
            # 打开PNG图片
            with Image.open(input_path) as img:
                # 如果图片有透明通道（RGBA），需要处理透明背景
                if img.mode in ('RGBA', 'LA'):
                    # 创建白色背景
                    background = Image.new('RGB', img.size, self.background_color)
                    # 将PNG图片粘贴到白色背景上
                    if img.mode == 'RGBA':
                        background.paste(img, mask=img.split()[-1])  # 使用alpha通道作为mask
                    else:  # LA模式
                        background.paste(img, mask=img.split()[-1])
                    img = background
                elif img.mode != 'RGB':
                    # 转换其他模式到RGB
                    img = img.convert('RGB')
                
                # 保存为JPG格式
                img.save(output_path, 'JPEG', quality=self.quality, optimize=True)
                
            print(f"转换成功: {input_path} -> {output_path}")
            return True
            
        except Exception as e:
            print(f"转换失败 {input_path}: {str(e)}")
            return False
    
    def convert_folder(self, input_folder, output_folder=None, recursive=False):
        """
        批量转换文件夹中的PNG图片
        
        Args:
            input_folder (str): 输入文件夹路径
            output_folder (str): 输出文件夹路径，如果为None则在原文件夹生成
            recursive (bool): 是否递归处理子文件夹
            
        Returns:
            tuple: (成功数量, 总数量)
        """
        if not os.path.exists(input_folder):
            print(f"错误：输入文件夹不存在 - {input_folder}")
            return 0, 0
        
        # 设置输出文件夹
        if output_folder is None:
            output_folder = input_folder
        else:
            os.makedirs(output_folder, exist_ok=True)
        
        success_count = 0
        total_count = 0
        
        # 获取PNG文件列表
        if recursive:
            png_files = list(Path(input_folder).rglob("*.png"))
        else:
            png_files = list(Path(input_folder).glob("*.png"))
        
        print(f"找到 {len(png_files)} 个PNG文件")
        
        for png_file in png_files:
            total_count += 1
            
            # 计算相对路径（用于保持文件夹结构）
            rel_path = png_file.relative_to(input_folder)
            output_path = Path(output_folder) / rel_path.with_suffix('.jpg')
            
            # 创建输出目录
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 转换图片
            if self.convert_single_image(str(png_file), str(output_path)):
                success_count += 1
        
        print(f"\n批量转换完成！成功: {success_count}/{total_count}")
        return success_count, total_count


def main():
    """主函数 - 命令行接口"""
    parser = argparse.ArgumentParser(description='PNG转JPG格式转换工具')
    parser.add_argument('input', help='输入文件或文件夹路径')
    parser.add_argument('-o', '--output', help='输出文件或文件夹路径')
    parser.add_argument('-q', '--quality', type=int, default=95, 
                       help='JPG质量 (1-100)，默认95')
    parser.add_argument('-r', '--recursive', action='store_true',
                       help='递归处理子文件夹')
    parser.add_argument('--bg-color', nargs=3, type=int, default=[255, 255, 255],
                       help='透明背景替换颜色 (R G B)，默认白色')
    
    args = parser.parse_args()
    
    # 创建转换器
    converter = PngToJpgConverter(
        quality=args.quality,
        background_color=tuple(args.bg_color)
    )
    
    # 判断输入是文件还是文件夹
    if os.path.isfile(args.input):
        # 单文件转换
        converter.convert_single_image(args.input, args.output)
    elif os.path.isdir(args.input):
        # 文件夹批量转换
        converter.convert_folder(args.input, args.output, args.recursive)
    else:
        print(f"错误：输入路径不存在 - {args.input}")
        sys.exit(1)


# ================================
# 使用示例
# ================================
def example_usage():
    """使用示例"""
    print("=== PNG转JPG转换工具使用示例 ===\n")
    
    # 创建转换器实例
    converter = PngToJpgConverter(quality=90)
    
    # 示例1：转换单个文件
    print("示例1：转换单个文件")
    # converter.convert_single_image("input.png", "output.jpg")
    
    # 示例2：批量转换文件夹
    print("示例2：批量转换文件夹")
    # converter.convert_folder("input_folder", "output_folder")
    
    # 示例3：递归转换（包含子文件夹）
    print("示例3：递归转换")
    # converter.convert_folder("input_folder", "output_folder", recursive=True)
    
    print("\n命令行使用方法：")
    print("python png_to_jpg_converter.py input.png -o output.jpg")
    print("python png_to_jpg_converter.py input_folder -o output_folder -r")
    print("python png_to_jpg_converter.py input_folder -q 85 --bg-color 255 255 255")


if __name__ == "__main__":
    if len(sys.argv) == 1:
        # 如果没有命令行参数，显示使用示例
        example_usage()
    else:
        # 运行主程序
        main()
