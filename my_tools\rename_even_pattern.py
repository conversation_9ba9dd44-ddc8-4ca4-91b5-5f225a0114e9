#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
偶数模式重命名工具

专门处理111文件夹中按偶数递增的文件名模式
将222文件夹的文件按顺序重命名为对应的frame_XXXXXX.png格式

对应关系：
00000000_pred.png -> frame_000000.png
00000001_pred.png -> frame_000002.png  
00000002_pred.png -> frame_000004.png
00000003_pred.png -> frame_000006.png
...
"""

import os
import shutil
import re


def get_frame_number_from_filename(filename):
    """
    从frame_XXXXXX.png格式的文件名中提取数字
    
    Args:
        filename (str): 文件名
    
    Returns:
        int: 提取的数字，如果格式不匹配返回-1
    """
    match = re.search(r'frame_(\d+)', filename)
    if match:
        return int(match.group(1))
    return -1


def generate_frame_name(index, extension='.png'):
    """
    根据索引生成frame格式的文件名
    
    Args:
        index (int): 文件索引（从0开始）
        extension (str): 文件扩展名
    
    Returns:
        str: 生成的文件名
    """
    # 按偶数递增：0->000000, 1->000002, 2->000004, 3->000006...
    frame_number = index * 2
    return f"frame_{frame_number:06d}{extension}"


def analyze_reference_folder():
    """分析111文件夹的实际文件名模式"""
    
    reference_folder = "C:/qyh/mmsegmentation-main/my_tools/out/111"
    
    print("🔍 分析111文件夹的文件名模式...")
    print("-" * 50)
    
    if not os.path.exists(reference_folder):
        print(f"❌ 参考文件夹不存在: {reference_folder}")
        return []
    
    # 获取所有frame文件
    frame_files = []
    for filename in os.listdir(reference_folder):
        if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
            frame_number = get_frame_number_from_filename(filename)
            if frame_number >= 0:
                frame_files.append((filename, frame_number))
    
    # 按frame编号排序
    frame_files.sort(key=lambda x: x[1])
    
    print(f"📊 找到 {len(frame_files)} 个frame文件")
    
    if frame_files:
        print("📋 前10个文件的编号:")
        for i, (filename, number) in enumerate(frame_files[:10]):
            print(f"  {i+1:2d}. {filename} (编号: {number})")
        
        if len(frame_files) > 10:
            print(f"  ... 还有 {len(frame_files) - 10} 个文件")
        
        # 分析编号规律
        if len(frame_files) >= 2:
            numbers = [num for _, num in frame_files[:10]]
            differences = [numbers[i+1] - numbers[i] for i in range(len(numbers)-1)]
            if differences:
                print(f"🔍 编号差值: {differences}")
                if len(set(differences)) == 1:
                    print(f"✅ 检测到规律: 每次递增 {differences[0]}")
    
    return [filename for filename, _ in frame_files]


def preview_rename():
    """预览重命名效果"""
    
    source_folder = "C:/qyh/mmsegmentation-main/my_tools/out/222"
    
    print("\n👀 预览重命名效果...")
    print("-" * 50)
    
    if not os.path.exists(source_folder):
        print(f"❌ 源文件夹不存在: {source_folder}")
        return False
    
    # 获取源文件列表（按名称排序）
    source_files = sorted([f for f in os.listdir(source_folder) 
                          if f.lower().endswith(('.png', '.jpg', '.jpeg'))])
    
    if not source_files:
        print("❌ 源文件夹中没有图片文件")
        return False
    
    print(f"📊 源文件夹文件数量: {len(source_files)}")
    
    # 获取参考文件夹的实际文件名
    reference_files = analyze_reference_folder()
    
    print(f"\n📋 重命名预览 (前10个):")
    print("-" * 50)
    
    # 显示重命名预览
    preview_count = min(10, len(source_files))
    
    for i in range(preview_count):
        source_name = source_files[i]
        source_ext = os.path.splitext(source_name)[1]
        
        if i < len(reference_files):
            # 使用实际的参考文件名
            reference_name = reference_files[i]
            reference_base = os.path.splitext(reference_name)[0]
            new_name = reference_base + source_ext
        else:
            # 如果参考文件不够，按模式生成
            new_name = generate_frame_name(i, source_ext)
        
        print(f"{i+1:2d}. {source_name} -> {new_name}")
    
    if len(source_files) > 10:
        print(f"... 还有 {len(source_files) - 10} 个文件")
    
    return True


def execute_rename():
    """执行重命名"""
    
    source_folder = "C:/qyh/mmsegmentation-main/my_tools/out/222"
    
    print("\n🔄 开始执行重命名...")
    print("-" * 50)
    
    # 获取源文件列表
    source_files = sorted([f for f in os.listdir(source_folder) 
                          if f.lower().endswith(('.png', '.jpg', '.jpeg'))])
    
    if not source_files:
        print("❌ 没有可处理的文件")
        return
    
    # 获取参考文件列表
    reference_files = analyze_reference_folder()
    
    # 创建备份文件夹
    backup_folder = os.path.join(source_folder, "backup_even_pattern")
    os.makedirs(backup_folder, exist_ok=True)
    print(f"💾 备份文件夹: {backup_folder}")
    
    print(f"\n开始重命名 {len(source_files)} 个文件...")
    print("-" * 50)
    
    success_count = 0
    
    for i, source_name in enumerate(source_files):
        try:
            source_path = os.path.join(source_folder, source_name)
            backup_path = os.path.join(backup_folder, source_name)
            source_ext = os.path.splitext(source_name)[1]
            
            # 确定新文件名
            if i < len(reference_files):
                # 使用实际的参考文件名
                reference_name = reference_files[i]
                reference_base = os.path.splitext(reference_name)[0]
                new_name = reference_base + source_ext
            else:
                # 按模式生成
                new_name = generate_frame_name(i, source_ext)
            
            new_path = os.path.join(source_folder, new_name)
            
            # 检查目标文件是否已存在
            if os.path.exists(new_path) and new_path != source_path:
                print(f"[{i+1:3d}] ⚠️  跳过 {source_name} - 目标文件已存在: {new_name}")
                continue
            
            # 创建备份
            shutil.copy2(source_path, backup_path)
            
            # 执行重命名
            os.rename(source_path, new_path)
            
            print(f"[{i+1:3d}] ✅ {source_name} -> {new_name}")
            success_count += 1
            
        except Exception as e:
            print(f"[{i+1:3d}] ❌ {source_name} - 错误: {str(e)}")
    
    print("-" * 50)
    print(f"🎉 重命名完成！成功: {success_count}/{len(source_files)}")
    print(f"💾 原文件已备份到: {backup_folder}")


def main():
    """主函数"""
    print("🔄 偶数模式重命名工具")
    print("=" * 40)
    print("将222文件夹的文件重命名为frame_XXXXXX.png格式")
    print("按照偶数递增模式：000000, 000002, 000004, 000006...")
    print("=" * 40)
    
    # 预览重命名
    if not preview_rename():
        return
    
    # 确认执行
    print("\n" + "=" * 40)
    confirm = input("确认执行重命名吗？(y/n): ").strip().lower()
    
    if confirm in ['y', 'yes', '是']:
        execute_rename()
    else:
        print("❌ 操作已取消")


if __name__ == "__main__":
    main()
