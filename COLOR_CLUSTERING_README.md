# 图像颜色聚类工具

这个工具可以将图片中相似的RGB值进行聚类，并用更加区分明显的颜色替换，让不同区域的颜色对比更加鲜明。

## 功能特点

- 🎨 **智能颜色聚类**: 使用K-means算法将相似颜色归为一类
- 🌈 **高对比度颜色**: 自动生成区分度高的颜色替换原有颜色
- 📊 **可视化对比**: 提供原图与处理后图像的对比显示
- ⚙️ **灵活配置**: 可自定义聚类数量
- 🖼️ **多格式支持**: 支持常见图像格式

## 安装依赖

```bash
pip install -r color_clustering_requirements.txt
```

或者手动安装：

```bash
pip install opencv-python scikit-learn matplotlib numpy
```

## 使用方法

### 1. 命令行使用

```bash
# 基本使用（默认8种颜色）
python color_clustering_tool.py input_image.jpg

# 指定聚类数量
python color_clustering_tool.py input_image.jpg -n 6

# 指定输出路径
python color_clustering_tool.py input_image.jpg -o output_image.jpg

# 显示对比图像
python color_clustering_tool.py input_image.jpg --show
```

### 2. Python代码使用

```python
from color_clustering_tool import ColorClusteringTool
import cv2

# 创建工具实例
tool = ColorClusteringTool(n_clusters=8)

# 处理图像
clustered_image = tool.process_image("input.jpg", "output.jpg")

# 显示对比
original_image = cv2.imread("input.jpg")
tool.show_comparison(original_image, clustered_image)
```

### 3. 运行示例

```bash
python example_usage.py
```

这将运行多个示例：
- 基本使用示例
- 不同聚类数量对比
- 真实图像处理示例

## 参数说明

### ColorClusteringTool 类参数

- `n_clusters` (int): 聚类数量，即最终图像中不同颜色的数量（默认: 8）

### 命令行参数

- `input_image`: 输入图像路径（必需）
- `-n, --n_clusters`: 聚类数量（默认: 8）
- `-o, --output`: 输出图像路径（可选，默认自动生成）
- `--show`: 显示对比图像

## 工作原理

1. **颜色提取**: 将图像中的所有像素提取为RGB值数组
2. **K-means聚类**: 使用K-means算法将相似的颜色归为一类
3. **颜色生成**: 为每个聚类生成一个高对比度的替代颜色
4. **图像重建**: 用新颜色替换原有像素，生成最终图像

## 颜色生成策略

工具使用以下策略生成高对比度颜色：

1. **预定义颜色**: 优先使用预设的高对比度颜色（红、绿、蓝、黄等）
2. **HSV空间生成**: 如需更多颜色，在HSV颜色空间中使用黄金角度分割生成
3. **固定随机种子**: 确保每次运行结果一致

## 应用场景

- 🎯 **图像分割可视化**: 让分割结果更加清晰
- 🖼️ **艺术效果处理**: 创建卡通化或海报化效果
- 📊 **数据可视化**: 简化图像用于分析
- 🎨 **设计辅助**: 提取主要颜色方案

## 示例效果

运行示例代码后，你将看到：

- 原始图像与聚类后图像的对比
- 不同聚类数量的效果对比
- 使用的颜色调色板

## 注意事项

- 聚类数量越少，图像越简化，但可能丢失细节
- 聚类数量越多，保留细节越多，但颜色区分度可能降低
- 建议根据图像复杂度选择合适的聚类数量（通常3-15之间）

## 故障排除

### 常见问题

1. **无法读取图像**: 检查图像路径和格式是否正确
2. **内存不足**: 对于大图像，可以先缩放再处理
3. **颜色效果不理想**: 尝试调整聚类数量

### 性能优化

- 对于大图像，建议先缩放到合适尺寸
- 可以通过调整K-means的参数来平衡速度和效果

## 扩展功能

你可以基于这个工具进行扩展：

- 添加更多颜色生成策略
- 支持不同的聚类算法
- 添加图像预处理功能
- 实现批量处理功能
