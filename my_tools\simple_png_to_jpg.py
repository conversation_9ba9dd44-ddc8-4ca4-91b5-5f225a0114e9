#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版PNG转JPG转换工具
专门用于批量处理语义分割结果图像

功能：
- 批量转换指定文件夹中的PNG图片为JPG格式
- 自动处理透明背景（设为白色）
- 保持原始文件名结构
- 适用于语义分割结果的格式转换

使用方法：
1. 修改下面的输入输出路径
2. 直接运行脚本
"""

import os
from PIL import Image
from pathlib import Path


def convert_png_to_jpg(input_folder, output_folder, quality=95):
    """
    批量将PNG图片转换为JPG格式
    
    Args:
        input_folder (str): 输入PNG文件夹路径
        output_folder (str): 输出JPG文件夹路径
        quality (int): JPG图片质量 (1-100)
    """
    # 创建输出文件夹
    os.makedirs(output_folder, exist_ok=True)
    
    # 获取所有PNG文件
    png_files = list(Path(input_folder).glob("*.png"))
    
    if not png_files:
        print(f"在 {input_folder} 中没有找到PNG文件")
        return
    
    print(f"找到 {len(png_files)} 个PNG文件，开始转换...")
    
    success_count = 0
    
    for png_file in png_files:
        try:
            # 生成输出文件路径
            jpg_filename = png_file.stem + '.jpg'
            output_path = Path(output_folder) / jpg_filename
            
            # 打开PNG图片
            with Image.open(png_file) as img:
                # 如果有透明通道，转换为白色背景
                if img.mode in ('RGBA', 'LA'):
                    # 创建白色背景
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    # 粘贴PNG到白色背景上
                    if img.mode == 'RGBA':
                        background.paste(img, mask=img.split()[-1])
                    else:
                        background.paste(img, mask=img.split()[-1])
                    img = background
                elif img.mode != 'RGB':
                    # 转换其他模式到RGB
                    img = img.convert('RGB')
                
                # 保存为JPG
                img.save(output_path, 'JPEG', quality=quality, optimize=True)
                
            print(f"✓ {png_file.name} -> {jpg_filename}")
            success_count += 1
            
        except Exception as e:
            print(f"✗ 转换失败 {png_file.name}: {str(e)}")
    
    print(f"\n转换完成！成功转换 {success_count}/{len(png_files)} 个文件")
    print(f"输出文件夹: {output_folder}")


def main():
    """主函数 - 配置输入输出路径"""
    
    # ================================
    # 配置区域 - 修改这里的路径
    # ================================
    
    # 输入文件夹路径（包含PNG文件）
    input_folder = "C:/qyh/mmsegmentation-main/my_tools/out/result1/pred"
    
    # 输出文件夹路径（保存JPG文件）
    output_folder = "C:/qyh/mmsegmentation-main/my_tools/out/result1/pred_jpg"
    
    # JPG质量设置 (1-100，数值越高质量越好，文件越大)
    jpg_quality = 95
    
    # ================================
    # 执行转换
    # ================================
    
    print("=== PNG转JPG批量转换工具 ===")
    print(f"输入文件夹: {input_folder}")
    print(f"输出文件夹: {output_folder}")
    print(f"JPG质量: {jpg_quality}")
    print("-" * 50)
    
    # 检查输入文件夹是否存在
    if not os.path.exists(input_folder):
        print(f"错误：输入文件夹不存在 - {input_folder}")
        return
    
    # 执行转换
    convert_png_to_jpg(input_folder, output_folder, jpg_quality)


if __name__ == "__main__":
    main()


# ================================
# 其他常用路径示例（取消注释使用）
# ================================

def convert_other_folders():
    """转换其他文件夹的示例"""
    
    # 示例1：转换可视化结果
    # convert_png_to_jpg(
    #     "C:/qyh/mmsegmentation-main/my_tools/out/vis",
    #     "C:/qyh/mmsegmentation-main/my_tools/out/vis_jpg"
    # )
    
    # 示例2：转换原始数据
    # convert_png_to_jpg(
    #     "C:/qyh/mmsegmentation-main/my_data/tushuguan",
    #     "C:/qyh/mmsegmentation-main/my_data/tushuguan_jpg"
    # )
    
    # 示例3：转换result1的img文件夹
    # convert_png_to_jpg(
    #     "C:/qyh/mmsegmentation-main/my_tools/out/result1/img",
    #     "C:/qyh/mmsegmentation-main/my_tools/out/result1/img_jpg"
    # )
    
    pass


# 如果需要转换多个文件夹，可以取消下面的注释
# convert_other_folders()
