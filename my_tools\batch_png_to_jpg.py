#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用PNG转JPG批量转换工具

使用方法：
python batch_png_to_jpg.py
然后按提示输入路径即可
"""

import os
from PIL import Image
from pathlib import Path


def convert_png_to_jpg_batch(input_folder, output_folder=None, quality=95):
    """
    批量转换PNG为JPG
    
    Args:
        input_folder (str): 输入文件夹路径
        output_folder (str): 输出文件夹路径，如果为None则在输入文件夹创建jpg子文件夹
        quality (int): JPG质量
    """
    # 检查输入文件夹
    if not os.path.exists(input_folder):
        print(f"❌ 错误：输入文件夹不存在 - {input_folder}")
        return False
    
    # 设置输出文件夹
    if output_folder is None:
        output_folder = os.path.join(input_folder, "jpg_output")
    
    # 创建输出文件夹
    os.makedirs(output_folder, exist_ok=True)
    
    # 查找PNG文件
    png_files = []
    for ext in ['*.png', '*.PNG']:
        png_files.extend(list(Path(input_folder).glob(ext)))
    
    if not png_files:
        print(f"❌ 在 {input_folder} 中没有找到PNG文件")
        return False
    
    print(f"📁 输入文件夹: {input_folder}")
    print(f"📁 输出文件夹: {output_folder}")
    print(f"🖼️  找到 {len(png_files)} 个PNG文件")
    print(f"⚙️  JPG质量: {quality}")
    print("=" * 60)
    
    success_count = 0
    
    for i, png_file in enumerate(png_files, 1):
        try:
            # 生成输出文件名
            jpg_filename = png_file.stem + '.jpg'
            output_path = Path(output_folder) / jpg_filename
            
            # 打开并转换图片
            with Image.open(png_file) as img:
                # 处理透明背景
                if img.mode in ('RGBA', 'LA'):
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    if img.mode == 'RGBA':
                        background.paste(img, mask=img.split()[-1])
                    else:
                        background.paste(img, mask=img.split()[-1])
                    img = background
                elif img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # 保存JPG
                img.save(output_path, 'JPEG', quality=quality, optimize=True)
            
            # 显示进度
            print(f"[{i:3d}/{len(png_files)}] ✅ {png_file.name} -> {jpg_filename}")
            success_count += 1
            
        except Exception as e:
            print(f"[{i:3d}/{len(png_files)}] ❌ {png_file.name} - 错误: {str(e)}")
    
    print("=" * 60)
    print(f"🎉 转换完成！成功: {success_count}/{len(png_files)}")
    
    if success_count > 0:
        print(f"📂 输出位置: {output_folder}")
    
    return success_count > 0


def interactive_convert():
    """交互式转换"""
    print("🖼️  PNG转JPG批量转换工具")
    print("=" * 40)
    
    # 获取输入路径
    while True:
        input_path = input("请输入PNG文件夹路径: ").strip().strip('"')
        if os.path.exists(input_path):
            break
        print("❌ 路径不存在，请重新输入")
    
    # 获取输出路径
    output_path = input("请输入输出文件夹路径（直接回车使用默认）: ").strip().strip('"')
    if not output_path:
        output_path = None
    
    # 获取质量设置
    while True:
        quality_input = input("请输入JPG质量 (1-100，直接回车使用95): ").strip()
        if not quality_input:
            quality = 95
            break
        try:
            quality = int(quality_input)
            if 1 <= quality <= 100:
                break
            else:
                print("❌ 质量值必须在1-100之间")
        except ValueError:
            print("❌ 请输入有效的数字")
    
    print("\n开始转换...")
    convert_png_to_jpg_batch(input_path, output_path, quality)


def quick_convert_examples():
    """快速转换示例（修改路径后使用）"""
    
    # 示例1：转换pred文件夹
    convert_png_to_jpg_batch(
        "C:/qyh/mmsegmentation-main/my_tools/out/result1/pred",
        "C:/qyh/mmsegmentation-main/my_tools/out/result1/pred_jpg"
    )
    
    # 示例2：转换vis文件夹
    # convert_png_to_jpg_batch(
    #     "C:/qyh/mmsegmentation-main/my_tools/out/vis",
    #     "C:/qyh/mmsegmentation-main/my_tools/out/vis_jpg"
    # )
    
    # 示例3：转换img文件夹
    # convert_png_to_jpg_batch(
    #     "C:/qyh/mmsegmentation-main/my_tools/out/result1/img",
    #     "C:/qyh/mmsegmentation-main/my_tools/out/result1/img_jpg"
    # )


if __name__ == "__main__":
    # 选择运行模式
    print("请选择运行模式：")
    print("1. 交互式转换（推荐）")
    print("2. 快速转换示例")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == "1":
        interactive_convert()
    elif choice == "2":
        print("运行快速转换示例...")
        quick_convert_examples()
    else:
        print("无效选择，使用交互式模式...")
        interactive_convert()
