#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
按模式重命名工具

根据111文件夹中的实际文件名模式，将222文件夹的文件重命名
支持非连续编号的情况（如：000000, 000002, 000004, 000006...）

对应关系：
222文件夹: 00000000_pred.png -> 111文件夹: frame_000000.png
222文件夹: 00000001_pred.png -> 111文件夹: frame_000002.png
222文件夹: 00000002_pred.png -> 111文件夹: frame_000004.png
222文件夹: 00000003_pred.png -> 111文件夹: frame_000006.png
...
"""

import os
import shutil
import re
from pathlib import Path


def extract_number_from_filename(filename):
    """
    从文件名中提取数字
    
    Args:
        filename (str): 文件名
    
    Returns:
        int: 提取的数字，如果没找到返回-1
    """
    # 查找文件名中的数字
    numbers = re.findall(r'\d+', filename)
    if numbers:
        # 返回最长的数字（通常是主要的编号）
        return int(max(numbers, key=len))
    return -1


def get_sorted_files_with_numbers(folder_path):
    """
    获取文件夹中的图片文件，并按文件名中的数字排序
    
    Args:
        folder_path (str): 文件夹路径
    
    Returns:
        list: [(文件名, 数字)] 的列表，按数字排序
    """
    if not os.path.exists(folder_path):
        return []
    
    files = []
    for filename in os.listdir(folder_path):
        if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp')):
            number = extract_number_from_filename(filename)
            files.append((filename, number))
    
    # 按数字排序
    files.sort(key=lambda x: x[1])
    return files


def preview_rename_pattern():
    """预览重命名模式"""
    
    source_folder = "C:/qyh/mmsegmentation-main/my_tools/out/222"
    reference_folder = "C:/qyh/mmsegmentation-main/my_tools/out/111"
    
    print("👀 分析文件名模式...")
    print("-" * 60)
    
    # 检查文件夹是否存在
    if not os.path.exists(source_folder):
        print(f"❌ 源文件夹不存在: {source_folder}")
        return False
    
    if not os.path.exists(reference_folder):
        print(f"❌ 参考文件夹不存在: {reference_folder}")
        return False
    
    # 获取文件列表
    source_files = get_sorted_files_with_numbers(source_folder)
    reference_files = get_sorted_files_with_numbers(reference_folder)
    
    print(f"📊 源文件夹 (222) 文件数量: {len(source_files)}")
    print(f"📊 参考文件夹 (111) 文件数量: {len(reference_files)}")
    
    if not source_files:
        print("❌ 源文件夹中没有图片文件")
        return False
    
    if not reference_files:
        print("❌ 参考文件夹中没有图片文件")
        return False
    
    # 分析参考文件夹的编号模式
    print("\n📋 参考文件夹 (111) 的文件名模式:")
    reference_numbers = [num for _, num in reference_files[:10]]
    for i, (filename, number) in enumerate(reference_files[:10]):
        print(f"  {i+1:2d}. {filename} (编号: {number})")
    
    if len(reference_files) > 10:
        print(f"  ... 还有 {len(reference_files) - 10} 个文件")
    
    # 分析编号规律
    if len(reference_numbers) >= 2:
        differences = [reference_numbers[i+1] - reference_numbers[i] for i in range(len(reference_numbers)-1)]
        if len(set(differences)) == 1:
            step = differences[0]
            print(f"\n🔍 检测到编号规律: 每次递增 {step}")
        else:
            print(f"\n🔍 编号差值: {differences}")
    
    print("\n📋 重命名预览 (前10个):")
    print("-" * 60)
    
    min_count = min(len(source_files), len(reference_files))
    
    for i in range(min(10, min_count)):
        source_name = source_files[i][0]
        reference_name = reference_files[i][0]
        
        # 生成新名称（保持源文件扩展名）
        reference_base = os.path.splitext(reference_name)[0]
        source_ext = os.path.splitext(source_name)[1]
        new_name = reference_base + source_ext
        
        print(f"{i+1:2d}. {source_name} -> {new_name}")
    
    if min_count > 10:
        print(f"... 还有 {min_count - 10} 个文件")
    
    print("-" * 60)
    return True


def rename_with_pattern():
    """按模式执行重命名"""
    
    source_folder = "C:/qyh/mmsegmentation-main/my_tools/out/222"
    reference_folder = "C:/qyh/mmsegmentation-main/my_tools/out/111"
    
    print("🔄 开始按模式重命名...")
    print(f"📁 源文件夹: {source_folder}")
    print(f"📁 参考文件夹: {reference_folder}")
    print("-" * 60)
    
    # 获取文件列表
    source_files = get_sorted_files_with_numbers(source_folder)
    reference_files = get_sorted_files_with_numbers(reference_folder)
    
    min_count = min(len(source_files), len(reference_files))
    
    if min_count == 0:
        print("❌ 没有可处理的文件")
        return
    
    # 创建备份文件夹
    backup_folder = os.path.join(source_folder, "backup_pattern")
    os.makedirs(backup_folder, exist_ok=True)
    print(f"💾 备份文件夹: {backup_folder}")
    
    print(f"\n开始重命名 {min_count} 个文件...")
    print("-" * 60)
    
    success_count = 0
    
    for i in range(min_count):
        try:
            source_name = source_files[i][0]
            reference_name = reference_files[i][0]
            
            # 构建路径
            source_path = os.path.join(source_folder, source_name)
            backup_path = os.path.join(backup_folder, source_name)
            
            # 生成新名称
            reference_base = os.path.splitext(reference_name)[0]
            source_ext = os.path.splitext(source_name)[1]
            new_name = reference_base + source_ext
            new_path = os.path.join(source_folder, new_name)
            
            # 检查新文件名是否已存在
            if os.path.exists(new_path) and new_path != source_path:
                print(f"[{i+1:3d}] ⚠️  跳过 {source_name} - 目标文件已存在: {new_name}")
                continue
            
            # 创建备份
            shutil.copy2(source_path, backup_path)
            
            # 执行重命名
            os.rename(source_path, new_path)
            
            print(f"[{i+1:3d}] ✅ {source_name} -> {new_name}")
            success_count += 1
            
        except Exception as e:
            print(f"[{i+1:3d}] ❌ {source_name} - 错误: {str(e)}")
    
    print("-" * 60)
    print(f"🎉 重命名完成！成功: {success_count}/{min_count}")
    print(f"💾 原文件已备份到: {backup_folder}")


def analyze_folder_pattern(folder_path, folder_name):
    """分析文件夹的文件名模式"""
    
    print(f"\n🔍 分析 {folder_name} 文件夹的文件名模式:")
    print("-" * 40)
    
    files = get_sorted_files_with_numbers(folder_path)
    
    if not files:
        print("❌ 没有找到图片文件")
        return
    
    print(f"📊 文件数量: {len(files)}")
    
    # 显示前几个文件
    for i, (filename, number) in enumerate(files[:5]):
        print(f"  {i+1}. {filename} (编号: {number})")
    
    if len(files) > 5:
        print(f"  ... 还有 {len(files) - 5} 个文件")
    
    # 分析编号规律
    if len(files) >= 2:
        numbers = [num for _, num in files]
        differences = [numbers[i+1] - numbers[i] for i in range(min(10, len(numbers)-1))]
        
        if len(set(differences)) == 1:
            print(f"🔍 编号规律: 每次递增 {differences[0]}")
        else:
            print(f"🔍 编号差值: {differences[:5]}{'...' if len(differences) > 5 else ''}")


def main():
    """主函数"""
    print("🔄 按模式重命名工具")
    print("=" * 50)
    
    source_folder = "C:/qyh/mmsegmentation-main/my_tools/out/222"
    reference_folder = "C:/qyh/mmsegmentation-main/my_tools/out/111"
    
    # 分析两个文件夹的模式
    analyze_folder_pattern(source_folder, "222 (源文件夹)")
    analyze_folder_pattern(reference_folder, "111 (参考文件夹)")
    
    print("\n" + "=" * 50)
    
    # 预览重命名
    if not preview_rename_pattern():
        return
    
    # 确认执行
    print("\n" + "=" * 50)
    confirm = input("确认按此模式执行重命名吗？(y/n): ").strip().lower()
    
    if confirm in ['y', 'yes', '是']:
        rename_with_pattern()
    else:
        print("❌ 操作已取消")


if __name__ == "__main__":
    main()
