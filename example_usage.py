#!/usr/bin/env python3
"""
颜色聚类工具使用示例
"""

import cv2
import numpy as np
from color_clustering_tool import ColorClusteringTool
import os


def create_sample_image():
    """创建一个示例图像用于测试"""
    # 创建一个包含多种相似颜色的测试图像
    image = np.zeros((300, 400, 3), dtype=np.uint8)
    
    # 添加一些颜色区域
    # 红色系区域
    image[50:100, 50:150] = [50, 50, 200]   # 深红
    image[50:100, 150:250] = [80, 80, 220]  # 稍浅红
    image[50:100, 250:350] = [100, 100, 255] # 浅红
    
    # 绿色系区域
    image[100:150, 50:150] = [50, 200, 50]   # 深绿
    image[100:150, 150:250] = [80, 220, 80]  # 稍浅绿
    image[100:150, 250:350] = [100, 255, 100] # 浅绿
    
    # 蓝色系区域
    image[150:200, 50:150] = [200, 50, 50]   # 深蓝
    image[150:200, 150:250] = [220, 80, 80]  # 稍浅蓝
    image[150:200, 250:350] = [255, 100, 100] # 浅蓝
    
    # 混合区域
    image[200:250, 50:200] = [150, 150, 150] # 灰色
    image[200:250, 200:350] = [180, 180, 180] # 浅灰色
    
    return image


def demo_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 创建示例图像
    sample_image = create_sample_image()
    cv2.imwrite("sample_image.jpg", sample_image)
    print("已创建示例图像: sample_image.jpg")
    
    # 创建颜色聚类工具
    tool = ColorClusteringTool(n_clusters=6)
    
    # 处理图像
    clustered_image = tool.process_image("sample_image.jpg", "sample_clustered.jpg")
    
    # 显示对比
    tool.show_comparison(sample_image, clustered_image)


def demo_different_cluster_numbers():
    """不同聚类数量的对比示例"""
    print("\n=== 不同聚类数量对比 ===")
    
    # 使用demo目录中的图像（如果存在）
    demo_image_path = "demo/demo.png"
    if not os.path.exists(demo_image_path):
        # 如果demo图像不存在，使用示例图像
        demo_image_path = "sample_image.jpg"
        if not os.path.exists(demo_image_path):
            sample_image = create_sample_image()
            cv2.imwrite(demo_image_path, sample_image)
    
    original_image = cv2.imread(demo_image_path)
    
    # 测试不同的聚类数量
    cluster_numbers = [3, 6, 10, 15]
    
    import matplotlib.pyplot as plt
    
    plt.figure(figsize=(20, 5))
    
    # 显示原图
    plt.subplot(1, len(cluster_numbers) + 1, 1)
    plt.imshow(cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB))
    plt.title('原始图像')
    plt.axis('off')
    
    # 显示不同聚类数量的结果
    for i, n_clusters in enumerate(cluster_numbers):
        tool = ColorClusteringTool(n_clusters=n_clusters)
        clustered_image = tool.cluster_colors(original_image)
        
        plt.subplot(1, len(cluster_numbers) + 1, i + 2)
        plt.imshow(cv2.cvtColor(clustered_image, cv2.COLOR_BGR2RGB))
        plt.title(f'{n_clusters} 种颜色')
        plt.axis('off')
        
        # 保存结果
        output_path = f"demo_clustered_{n_clusters}colors.jpg"
        cv2.imwrite(output_path, clustered_image)
        print(f"已保存: {output_path}")
    
    plt.tight_layout()
    plt.show()


def demo_real_image():
    """处理真实图像的示例"""
    print("\n=== 处理真实图像示例 ===")
    
    # 检查是否有可用的图像
    possible_images = [
        "demo/demo.png",
        "demo/classroom__rgb_00283.jpg",
        "resources/3dogs.jpg"
    ]
    
    image_path = None
    for path in possible_images:
        if os.path.exists(path):
            image_path = path
            break
    
    if image_path is None:
        print("未找到可用的示例图像，跳过真实图像处理示例")
        return
    
    print(f"使用图像: {image_path}")
    
    # 创建工具并处理
    tool = ColorClusteringTool(n_clusters=8)
    original_image = cv2.imread(image_path)
    clustered_image = tool.process_image(image_path, f"real_image_clustered.jpg")
    
    # 显示结果
    tool.show_comparison(original_image, clustered_image)


if __name__ == "__main__":
    print("颜色聚类工具使用示例")
    print("=" * 50)
    
    try:
        # 基本使用示例
        demo_basic_usage()
        
        # 不同聚类数量对比
        demo_different_cluster_numbers()
        
        # 真实图像处理
        demo_real_image()
        
        print("\n所有示例运行完成！")
        
    except Exception as e:
        print(f"运行示例时出现错误: {e}")
        import traceback
        traceback.print_exc()
