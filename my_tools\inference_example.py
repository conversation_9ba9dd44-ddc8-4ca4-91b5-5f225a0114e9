#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语义分割批量推理脚本
使用MMSegmentation框架对图像进行批量语义分割推理

功能：
1. 从指定文件夹读取图像
2. 使用训练好的SegFormer模型进行语义分割
3. 批量处理图像（每10张为一批）
4. 保存推理结果和可视化结果
5. 记录处理进度日志

输入：C:\csy\converted_png 文件夹中的图像文件
输出：out/111/ (可视化结果), out/222/ (预测结果), stamp.txt (处理日志)
"""

from mmseg.apis import MMSegInferencer
import os

# ================================
# 1. 初始化语义分割推理器
# ================================
# 创建MMSegmentation推理器实例
inferencer = MMSegInferencer(
    model='C:/qyh/mmsegmentation-main/configs/segformer/segformer_whu.py',  # SegFormer模型配置文件
    weights='C:/qyh/mmsegmentation-main/my_model/iter_160000.pth',          # 训练好的模型权重（160k次迭代）
    device='cuda'  # 使用GPU加速推理，如果没有GPU可改为'cpu'
)

# ================================
# 2. 准备输入数据
# ================================
dirname = 'F:/frames'  # 输入图像文件夹路径
filelist = sorted(os.listdir(dirname))  # 获取文件夹中所有文件并按名称排序
imgpaths = []  # 存储完整图像路径的列表
cc = 0  # 计数器，记录已处理的图像数量
fp = open('stamp.txt', 'wt')  # 创建日志文件，记录处理进度
ss = ''  # 临时字符串，用于批量写入日志

# ================================
# 3. 批量处理图像（每10张为一批）
# ================================
for ff in filelist:
    # 构建完整的图像路径并添加到路径列表中
    imgpaths.append(os.path.join(dirname, ff))
    cc += 1  # 增加计数器
    ss += ff + '\n'  # 将当前文件名添加到日志字符串中

    # 每处理10张图像进行一次批量推理
    if cc % 10 == 0:
        # 对最近的10张图像进行语义分割推理
        inferencer(
            imgpaths[-10:],           # 输入：最近的10张图像路径
            show=True,                # 显示推理结果（弹窗显示）
            wait_time=0.001,          # 显示等待时间（0.001秒）
            out_dir='out',            # 输出根目录
            img_out_dir='333',        # 可视化结果保存子目录
            pred_out_dir='444'        # 预测结果保存子目录（原始预测掩码）
        )
        # 将当前批次的处理记录写入日志文件
        fp.writelines(ss)
        fp.flush()  # 强制刷新文件缓冲区，确保数据写入磁盘
        ss = ''     # 清空临时日志字符串

# ================================
# 4. 处理剩余图像
# ================================
# 如果总图像数不是10的倍数，处理剩余的图像
if cc % 10 != 0:
    # 对剩余的图像进行语义分割推理
    inferencer(
        imgpaths[-(cc % 10):],    # 输入：剩余的图像路径
        show=True,                # 显示推理结果
        wait_time=0.001,          # 显示等待时间
        out_dir='out',            # 输出根目录
        img_out_dir='111',        # 可视化结果保存子目录
        pred_out_dir='222'        # 预测结果保存子目录
    )
    # 写入最后一批的处理记录
    fp.writelines(ss)
    fp.flush()

# ================================
# 5. 清理资源并输出完成信息
# ================================
fp.close()  # 关闭日志文件

# 输出处理完成信息
print(f"批量语义分割推理完成！")
print(f"共处理了 {cc} 张图像")
print("输出结果保存位置：")
print("- 可视化结果（彩色分割图）: out/111/")
print("- 预测结果（原始掩码）: out/222/")
print("- 处理进度日志: stamp.txt")