#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单文件名匹配重命名工具

将222文件夹的文件名改成与111文件夹一致
例如：00000000_pred.png -> frame_000000.png

使用方法：直接运行即可
"""

import os
import shutil
from pathlib import Path


def simple_rename_to_match():
    """简单的文件名匹配重命名"""
    
    # 文件夹路径
    source_folder = "C:/qyh/mmsegmentation-main/my_tools/out/222"  # 需要重命名的文件夹
    reference_folder = "C:/qyh/mmsegmentation-main/my_tools/out/111"  # 参考文件夹
    
    print("🔄 开始重命名文件...")
    print(f"源文件夹: {source_folder}")
    print(f"参考文件夹: {reference_folder}")
    print("-" * 50)
    
    # 检查文件夹是否存在
    if not os.path.exists(source_folder):
        print(f"❌ 源文件夹不存在: {source_folder}")
        return
    
    if not os.path.exists(reference_folder):
        print(f"❌ 参考文件夹不存在: {reference_folder}")
        return
    
    # 获取文件列表并排序
    source_files = sorted([f for f in os.listdir(source_folder) 
                          if f.lower().endswith(('.png', '.jpg', '.jpeg'))])
    reference_files = sorted([f for f in os.listdir(reference_folder) 
                             if f.lower().endswith(('.png', '.jpg', '.jpeg'))])
    
    print(f"源文件夹图片数量: {len(source_files)}")
    print(f"参考文件夹图片数量: {len(reference_files)}")
    
    if len(source_files) == 0:
        print("❌ 源文件夹中没有图片文件")
        return
    
    if len(reference_files) == 0:
        print("❌ 参考文件夹中没有图片文件")
        return
    
    # 检查数量是否匹配
    if len(source_files) != len(reference_files):
        print("⚠️  警告：文件数量不匹配！")
        min_count = min(len(source_files), len(reference_files))
        print(f"将处理前 {min_count} 个文件")
        source_files = source_files[:min_count]
        reference_files = reference_files[:min_count]
    
    # 创建备份文件夹
    backup_folder = os.path.join(source_folder, "backup")
    os.makedirs(backup_folder, exist_ok=True)
    print(f"💾 备份文件夹: {backup_folder}")
    
    print("\n开始重命名...")
    print("-" * 50)
    
    success_count = 0
    
    # 执行重命名
    for i, (source_file, reference_file) in enumerate(zip(source_files, reference_files)):
        try:
            # 完整路径
            source_path = os.path.join(source_folder, source_file)
            backup_path = os.path.join(backup_folder, source_file)
            
            # 获取参考文件的名称（保持原扩展名）
            reference_name = os.path.splitext(reference_file)[0]
            source_ext = os.path.splitext(source_file)[1]
            new_name = reference_name + source_ext
            new_path = os.path.join(source_folder, new_name)
            
            # 创建备份
            shutil.copy2(source_path, backup_path)
            
            # 重命名文件
            os.rename(source_path, new_path)
            
            print(f"[{i+1:3d}] ✅ {source_file} -> {new_name}")
            success_count += 1
            
        except Exception as e:
            print(f"[{i+1:3d}] ❌ {source_file} - 错误: {str(e)}")
    
    print("-" * 50)
    print(f"🎉 重命名完成！成功: {success_count}/{len(source_files)}")
    print(f"💾 原文件已备份到: {backup_folder}")


def preview_rename():
    """预览重命名效果"""
    
    source_folder = "C:/qyh/mmsegmentation-main/my_tools/out/222"
    reference_folder = "C:/qyh/mmsegmentation-main/my_tools/out/111"
    
    print("👀 预览重命名效果...")
    print("-" * 50)
    
    if not os.path.exists(source_folder) or not os.path.exists(reference_folder):
        print("❌ 文件夹不存在")
        return
    
    source_files = sorted([f for f in os.listdir(source_folder) 
                          if f.lower().endswith(('.png', '.jpg', '.jpeg'))])
    reference_files = sorted([f for f in os.listdir(reference_folder) 
                             if f.lower().endswith(('.png', '.jpg', '.jpeg'))])
    
    min_count = min(len(source_files), len(reference_files), 10)  # 只显示前10个
    
    for i in range(min_count):
        source_name = source_files[i]
        reference_name = reference_files[i]
        
        # 生成新名称
        reference_base = os.path.splitext(reference_name)[0]
        source_ext = os.path.splitext(source_name)[1]
        new_name = reference_base + source_ext
        
        print(f"{i+1:2d}. {source_name} -> {new_name}")
    
    if len(source_files) > 10:
        print(f"... 还有 {len(source_files) - 10} 个文件")


def main():
    """主函数"""
    print("🔄 文件名匹配重命名工具")
    print("=" * 40)
    
    # 先预览
    preview_rename()
    
    # 确认执行
    print("\n" + "=" * 40)
    confirm = input("确认执行重命名吗？(y/n): ").strip().lower()
    
    if confirm in ['y', 'yes', '是']:
        simple_rename_to_match()
    else:
        print("❌ 操作已取消")


if __name__ == "__main__":
    main()
